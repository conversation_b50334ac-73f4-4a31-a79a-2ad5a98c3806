# mqtt_bulk_chunked_rolling.py
# 从多车JSON中按车游标，每次仅发布下一批(默认5个)waypoints到 agv/paths
# payload 形如 {"AGV1":[...], "AGV2":[...], ...}，但每批只带“尚未发送完的车”和“它们的下一段”

import json
import time
import re
import os
import paho.mqtt.client as mqtt

BROKER = "localhost"
PORT = 1883
CLIENT_ID = "PythonClient-BulkChunked"  # 确保唯一，避免互踢
JSON_FILE = r"C:\Users\<USER>\Desktop\模拟输入的多AGV方向点路径.json"

BATCH = 5           # 每次每台车发送的点数
INTERVAL = 2.0      # 批次间隔(秒)
TOPIC_BULK = "agv/paths"

# 若你的 Unity 侧设置了 SimulationManager.reinitOnPathMessage=false，
# 就是“滚动追加”。确保第一批每台车至少2个点且不全重合，否则车辆不会动。
MIN_POINTS_FIRST_CHUNK = 2

def tolerant_load(path: str):
    """尽量从文件解析出 Python 对象（dict 或 list）。"""
    with open(path, "r", encoding="utf-8-sig") as f:
        txt = f.read().strip()

    # 1) 标准 JSON
    try:
        return json.loads(txt)
    except Exception:
        pass

    # 2) 容错：多个对象缺少最外层 []
    try:
        return json.loads(f"[{txt}]")
    except Exception:
        pass

    # 3) 兜底：提取每个 {...}
    items = []
    for m in re.finditer(r'\{(?:[^{}]|(?R))*\}', txt, flags=re.DOTALL):
        chunk = m.group(0).rstrip('，, \t\r\n')
        try:
            items.append(json.loads(chunk))
        except Exception:
            pass

    # 常见两种：单个 dict 或 列表[dict]
    if len(items) == 1 and isinstance(items[0], dict):
        return items[0]
    if items:
        return items
    return None

def normalize_to_multi(obj):
    """
    归一化为 {AGVx:[...]}：
    - 已是 dict：直接返回
    - [dict]：取第 0 个
    其他形态报错
    """
    if isinstance(obj, dict) and obj:
        return obj
    if isinstance(obj, list) and len(obj) == 1 and isinstance(obj[0], dict):
        return obj[0]
    raise ValueError("无法识别为 {AGVx:[...]} 多车对象")

def validate_first_chunk(agv: str, chunk):
    """
    首批至少 2 点；且不全部重合（全部 loc 一样会导致起步0距离）。
    loc 格式仍保持原样 '(x,z)'，Unity 侧会解析。
    """
    if not isinstance(chunk, list) or len(chunk) < MIN_POINTS_FIRST_CHUNK:
        return False, f"{agv} 首批点数不足 {MIN_POINTS_FIRST_CHUNK}"
    # 粗略判断是否全重合
    locs = [str(it.get("loc", "")).strip() for it in chunk]
    uniq = {locs[0], locs[-1]} if locs else set()
    if len(uniq) == 1:
        return False, f"{agv} 首批首尾 loc 相同，可能无法起步（两点重合）"
    return True, "OK"

def on_connect(client, userdata, flags, reason_code, properties=None):
    if reason_code == 0:
        print("✅ Connected to broker")
    else:
        print(f"❌ Connect failed rc={reason_code}")

def main():
    # 1) 读取并规范为 {AGVx:[...]}
    raw = tolerant_load(JSON_FILE)
    if raw is None:
        print("❌ 读取/解析失败：无法识别任何 JSON 结构")
        return
    all_data = normalize_to_multi(raw)

    # 2) 为每台车建立游标
    #    只收录有有效列表的条目
    paths = {agv: arr for agv, arr in all_data.items() if isinstance(arr, list) and len(arr) > 0}
    if not paths:
        print("❌ 数据为空：没有任何 {AGV:[...]} 可用")
        return

    # 注意：保持确定性顺序发布（按车名排序），便于排查
    agv_ids = sorted(paths.keys(), key=lambda s: (len(s), s))

    cursors = {agv: 0 for agv in agv_ids}

    # 3) MQTT 连接（paho-mqtt 2.x：使用枚举声明回调API版本）
    client = mqtt.Client(
        client_id=CLIENT_ID,
        protocol=mqtt.MQTTv311,
        callback_api_version=mqtt.CallbackAPIVersion.VERSION2
    )
    client.on_connect = on_connect
    client.reconnect_delay_set(min_delay=1, max_delay=10)

    client.connect(BROKER, PORT, keepalive=60)
    client.loop_start()

    try:
        first_batch = True
        while True:
            bulk_payload = {}
            any_left = False

            for agv in agv_ids:
                arr = paths[agv]
                cur = cursors[agv]
                if cur >= len(arr):
                    continue
                any_left = True

                end = min(cur + BATCH, len(arr))
                chunk = arr[cur:end]

                # 首批时对每台车的首段做基本校验（避免 Unity 不动）
                if first_batch:
                    ok, msg = validate_first_chunk(agv, chunk)
                    if not ok:
                        print(f"⚠️ {msg}，将自动扩展到至少 {MIN_POINTS_FIRST_CHUNK} 个且首尾不同。")
                        # 尝试往后扩展直到找到一个不同 loc 或到数组末尾
                        expand_end = end
                        base_loc = str(chunk[0].get("loc", "")).strip() if chunk else ""
                        while expand_end < len(arr):
                            next_item = arr[expand_end]
                            if str(next_item.get("loc", "")).strip() != base_loc:
                                expand_end += 1
                                break
                            expand_end += 1
                        # 至少保证两点
                        expand_end = max(expand_end, cur + MIN_POINTS_FIRST_CHUNK)
                        expand_end = min(expand_end, len(arr))
                        chunk = arr[cur:expand_end]
                        end = expand_end

                bulk_payload[agv] = chunk
                cursors[agv] = end

            if not any_left:
                print("✅ All paths published.")
                break

            s = json.dumps(bulk_payload, ensure_ascii=False, separators=(",", ":"))
            r = client.publish(TOPIC_BULK, s, qos=1, retain=False)
            if r.rc == mqtt.MQTT_ERR_SUCCESS:
                totals = {k: len(v) for k, v in bulk_payload.items()}
                lefts = {k: max(0, len(paths[k]) - cursors[k]) for k in agv_ids}
                print(f"🚚 Published chunk to {TOPIC_BULK}: {totals} | remaining: {lefts}")
            else:
                print("❌ publish failed rc=", r.rc)

            first_batch = False
            time.sleep(INTERVAL)

    except KeyboardInterrupt:
        print("🛑 Stop by user")
    finally:
        client.loop_stop()
        client.disconnect()

if __name__ == "__main__":
    main()
