# mqtt_single_chunked_folder.py
# 从文件夹中读取多辆车的路径文件，按“单车主题”滚动发布：
#   主题： agv/{AGV_ID}/path
#   负载： [ { "rn":..., "loc":"(x,z)", "direction":"前/后/左/右", "status":"..." }, ... ]
# 每次每台车仅发布下一段 BATCH 个点，间隔 INTERVAL 秒，直到发完。

import json, time, re, os
from typing import Dict, List, Tuple, Optional
import paho.mqtt.client as mqtt

# ======== 配置 ========
BROKER = "localhost"
PORT = 1883
CLIENT_ID = "PythonClient-SingleChunkedFolder"
FOLDER = r"C:\Users\<USER>\Desktop\AGV路径"

BATCH = 5
INTERVAL = 2.0
TOPIC_TEMPLATE = "agv/{agv}/path"
MIN_POINTS_FIRST_CHUNK = 2
EXTS = {".json", ".txt"}

# ======== 工具函数 ========
def tolerant_load(path: str):
    with open(path, "r", encoding="utf-8-sig") as f:
        txt = f.read().strip()
    try:
        return json.loads(txt)
    except Exception:
        pass
    try:
        return json.loads(f"[{txt}]")
    except Exception:
        pass
    items = []
    for m in re.finditer(r'\{(?:[^{}]|(?R))*\}', txt, flags=re.DOTALL):
        try:
            items.append(json.loads(m.group(0).rstrip('，, \t\r\n')))
        except Exception:
            pass
    if len(items) == 1 and isinstance(items[0], dict):
        return items[0]
    if items:
        return items
    return None

def extract_agv_from_filename(filename: str) -> Optional[str]:
    m = re.search(r'(AGV\d+)', filename, flags=re.IGNORECASE)
    if m:
        return m.group(1).upper()
    return None

def normalize_to_single(obj, fallback_agv: Optional[str]) -> Tuple[Optional[str], Optional[List[dict]], str]:
    if isinstance(obj, dict) and obj:
        for k, v in obj.items():
            if isinstance(v, list):
                return k.strip(), v, "dict-with-key"
        return None, None, "dict-without-list"
    if isinstance(obj, list) and obj and isinstance(obj[0], dict):
        if not fallback_agv:
            return None, None, "pure-array-but-no-agv-id"
        return fallback_agv, obj, "pure-array"
    return None, None, "unrecognized-structure"

def validate_first_chunk(agv: str, chunk: List[dict]) -> Tuple[bool, str]:
    if not isinstance(chunk, list) or len(chunk) < MIN_POINTS_FIRST_CHUNK:
        return False, f"{agv} 首批点数不足 {MIN_POINTS_FIRST_CHUNK}"
    def loc_str(d): return str(d.get("loc", "")).strip()
    if len(chunk) >= 2 and loc_str(chunk[0]) == loc_str(chunk[-1]):
        return False, f"{agv} 首批首尾 loc 相同"
    return True, "OK"

# ======== MQTT 回调 ========
def on_connect(client, userdata, flags, rc, properties=None):
    print("✅ Connected" if rc == 0 else f"❌ Connect failed rc={rc}")

def on_disconnect(client, userdata, *rest):
    disconnect_flags = rest[0] if len(rest) >= 1 else None
    reason_code      = rest[1] if len(rest) >= 2 else None
    properties       = rest[2] if len(rest) >= 3 else None
    print(f"⚠️ Disconnected. flags={disconnect_flags} rc={reason_code} props={properties}")
    try:
        client.reconnect()
        print("↩️ reconnect issued")
    except Exception as e:
        print(f"❌ reconnect failed: {e}")

# ======== 主流程 ========
def main():
    if not os.path.isdir(FOLDER):
        print(f"❌ 路径不存在或不是文件夹：{FOLDER}")
        return

    agv_paths: Dict[str, List[dict]] = {}
    file_count = 0
    for name in os.listdir(FOLDER):
        full = os.path.join(FOLDER, name)
        if not os.path.isfile(full): continue
        _, ext = os.path.splitext(name)
        if ext.lower() not in EXTS: continue

        file_count += 1
        raw = tolerant_load(full)
        if raw is None:
            print(f"⛔ 跳过（无法解析）：{name}")
            continue

        fallback_agv = extract_agv_from_filename(name)
        agv, arr, mode = normalize_to_single(raw, fallback_agv)
        if agv is None or arr is None:
            print(f"⛔ 跳过（{mode}）：{name}")
            continue

        if agv in agv_paths: agv_paths[agv].extend(arr)
        else: agv_paths[agv] = list(arr)
        print(f"📄 载入 {name} -> {agv} ({len(arr)} points, mode={mode})")

    if not agv_paths:
        print("❌ 没有读到任何有效的 AGV 路径数据。")
        return
    print(f"✅ 总计 {len(agv_paths)} 辆车，来自 {file_count} 个文件。")

    agv_ids = sorted(agv_paths.keys(), key=lambda s: (len(s), s))
    cursors = {agv: 0 for agv in agv_ids}

    client = mqtt.Client(
        client_id=CLIENT_ID,
        protocol=mqtt.MQTTv311,
        callback_api_version=mqtt.CallbackAPIVersion.VERSION2
    )
    client.on_connect = on_connect
    client.on_disconnect = on_disconnect
    client.reconnect_delay_set(min_delay=1, max_delay=10)
    client.enable_logger()

    client.connect(BROKER, PORT, keepalive=60)
    client.loop_start()

    try:
        first_batch = True
        while True:
            any_left = False
            for agv in agv_ids:
                arr = agv_paths[agv]
                cur = cursors[agv]
                if cur >= len(arr): continue
                any_left = True

                end = min(cur + BATCH, len(arr))
                chunk = arr[cur:end]

                if first_batch:
                    ok, msg = validate_first_chunk(agv, chunk)
                    if not ok:
                        print(f"⚠️ {msg}，扩展点数")
                        expand_end = end
                        base_loc = str(chunk[0].get("loc", "")).strip() if chunk else ""
                        while expand_end < len(arr):
                            if str(arr[expand_end].get("loc", "")).strip() != base_loc:
                                expand_end += 1; break
                            expand_end += 1
                        expand_end = max(expand_end, cur + MIN_POINTS_FIRST_CHUNK)
                        expand_end = min(expand_end, len(arr))
                        chunk = arr[cur:expand_end]
                        end = expand_end

                topic = TOPIC_TEMPLATE.format(agv=agv)
                payload = json.dumps(chunk, ensure_ascii=False, separators=(",", ":"))
                r = client.publish(topic, payload, qos=1, retain=False)
                if r.rc == mqtt.MQTT_ERR_SUCCESS:
                    left = max(0, len(arr) - end)
                    print(f"🚚 {agv} -> {topic}: +{len(chunk)} (remaining {left})")
                else:
                    print(f"❌ publish failed rc={r.rc} for {agv}")

                cursors[agv] = end
                time.sleep(0.02)  # 节流

            if not any_left:
                print("✅ All AGV paths published.")
                break

            first_batch = False
            time.sleep(INTERVAL)

    except KeyboardInterrupt:
        print("🛑 Stop by user")
    finally:
        client.loop_stop()
        client.disconnect()

if __name__ == "__main__":
    main()
