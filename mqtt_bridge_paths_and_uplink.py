# -*- coding: utf-8 -*-
"""
mqtt_bridge_paths_and_uplink.py
- 下行：从文件夹读取多个 AGV 路径，按“单车主题”分批滚动发布：agv/{AGV_ID}/path
- 上行：订阅 Unity 回传的遥测并实时打印（兼容三种）：
        1) 单车：agv/{AGV_ID}/telemetry       ->  { ... }
        2) 批量：agv/telemetry                ->  [ {...}, {...} ]
        3) 批量：agv/telemetry                ->  {"entries":[ {...}, ... ]}

Unity 端常见上行 JSON：
  单车：{
    "time":1.5,"agvId":"AGV1",
    "x":1,"y":0,"z":2,"rx":0,"ry":90,"rz":0,
    "speed":1.2,"loaded":true,"status":"issued",
    "waypointCursor":23,"totalWaypoints":120
  }

  批量（两种都支持）：
    [
      {...AGV1...},
      {...AGV2...}
    ]
  或：
    { "entries":[ {...AGV1...}, {...AGV2...} ] }
"""
import json, time, re, os, sys
from typing import Dict, List, Tuple, Optional
import paho.mqtt.client as mqtt

# ========= 配置 =========
BROKER   = "127.0.0.1"
PORT     = 1883
USERNAME = ""     # 如无鉴权留空
PASSWORD = ""

CLIENT_ID = "PythonBridge-DownUp"                 # 确保唯一
FOLDER    = r"C:\Users\<USER>\Desktop\AGV路径"     # 多个AGV路径文件的文件夹

# 下行（路径）
BATCH         = 10
INTERVAL      = 2.0
TOPIC_SINGLE  = "agv/{agv}/path"
QOS_DOWN      = 1
MIN_POINTS_FIRST_CHUNK = 2

# 上行（遥测）——同时订阅“单车”和“批量”，兼容老 topic
UPLINK_TOPICS = ["agv/+/telemetry", "agv/telemetry", "agv/+/state"]
QOS_UP        = 1

# 允许的路径文件扩展名
EXTS = {".json", ".txt"}

# ========= 工具函数 =========
def tolerant_load(path: str):
    with open(path, "r", encoding="utf-8-sig") as f:
        txt = f.read().strip()
    # 标准 JSON
    try:
        return json.loads(txt)
    except Exception:
        pass
    # 多个对象缺少最外层 []
    try:
        return json.loads(f"[{txt}]")
    except Exception:
        pass
    # 兜底：逐个 { ... }
    items = []
    for m in re.finditer(r'\{(?:[^{}]|(?R))*\}', txt, flags=re.DOTALL):
        try:
            items.append(json.loads(m.group(0).rstrip('，, \t\r\n')))
        except Exception:
            pass
    if len(items) == 1 and isinstance(items[0], dict):
        return items[0]
    if items:
        return items
    return None

def extract_agv_from_filename(filename: str) -> Optional[str]:
    m = re.search(r'(AGV\d+)', filename, flags=re.IGNORECASE)
    return m.group(1).upper() if m else None

def normalize_to_single(obj, fallback_agv: Optional[str]) -> Tuple[Optional[str], Optional[List[dict]], str]:
    # {'AGVx': [...]}
    if isinstance(obj, dict) and obj:
        for k, v in obj.items():
            if isinstance(v, list):
                return str(k).strip(), v, "dict-with-key"
        return None, None, "dict-without-list"
    # [ {...}, {...} ] + 文件名推断 agv
    if isinstance(obj, list) and obj and isinstance(obj[0], dict):
        if not fallback_agv:
            return None, None, "pure-array-but-no-agv-id"
        return fallback_agv, obj, "pure-array"
    return None, None, "unrecognized-structure"

def validate_first_chunk(agv: str, chunk: List[dict]) -> Tuple[bool, str]:
    if not isinstance(chunk, list) or len(chunk) < MIN_POINTS_FIRST_CHUNK:
        return False, f"{agv} 首批点数不足 {MIN_POINTS_FIRST_CHUNK}"
    def loc_str(d): return str(d.get("loc", "")).strip()
    if len(chunk) >= 2 and loc_str(chunk[0]) == loc_str(chunk[-1]):
        return False, f"{agv} 首批首尾 loc 相同，可能无法起步（两点重合）"
    return True, "OK"

# ========= 上行打印辅助 =========
def _fmt_num(x, nd=2):
    try:
        return f"{float(x):.{nd}f}"
    except Exception:
        return "NA" if x is None else str(x)

def _current_loc_str(d: dict) -> str:
    """
    优先使用 currentLoc（形如 "(x,z)"）。
    兼容旧格式：若有 x/z 则拼成 "(x,z)"；若有 loc/pos 字符串也直接用。
    """
    s = d.get("currentLoc")
    if isinstance(s, str) and s:
        return s

    # 旧字段：x,z
    x, z = d.get("x"), d.get("z")
    if x is not None and z is not None:
        return f"({_fmt_num(x)},{_fmt_num(z)})"

    # 兜底：loc / pos
    s = d.get("loc") or d.get("pos")
    if isinstance(s, str) and s:
        return s

    return "NA"

def _angle_value(d: dict):
    """
    新字段 angle 优先；兼容旧字段 ry（度）。返回数值或 None。
    """
    ang = d.get("angle")
    if ang is None:
        ang = d.get("ry")
    return ang

# ========= 上行打印（兼容新老字段） =========
def _print_one_telemetry(topic: str, data: dict):
    # 新字段优先；无则兼容老字段
    eq_id   = data.get("equipmentId") or data.get("agvId") or data.get("agv") or "?"
    eq_type = data.get("equipmentType") or ""
    zone    = data.get("zone") or ""

    current_loc = data.get("currentLoc") or data.get("nextWaypoint") or ""

    cx = data.get("currentCoordinateX")
    cy = data.get("currentCoordinateY")
    if cx is None: cx = data.get("x")
    if cy is None: cy = data.get("y") or data.get("z")  # 老格式用 z 当平面Y

    angle  = data.get("angle")
    if angle is None: angle = data.get("ry")  # 兼容老字段

    speed  = data.get("speed")
    status = data.get("status")
    isload = data.get("isLoading")
    t      = data.get("time")

    # 控制台友好输出
    print(
        f"⬆️  {time.strftime('%H:%M:%S')} {topic} | "
        f"id={eq_id} type={eq_type or '-'} zone={zone or '-'} "
        f"isLoading={isload} currentLoc={current_loc or '-'} "
        f"coord=({_fmt_num(cx)},{_fmt_num(cy)}) "
        f"angle={_fmt_num(angle,1)} v={_fmt_num(speed)} "
        f"status={status} t={_fmt_num(t)}"
    )



def _handle_uplink_payload(topic: str, payload: str):
    # 支持：dict / list / {"entries":[...]}
    try:
        obj = json.loads(payload)
    except Exception:
        obj = None

    if isinstance(obj, dict):
        # 批量包裹：{"entries":[...]}
        if isinstance(obj.get("entries"), list):
            for e in obj["entries"]:
                if isinstance(e, dict):
                    _print_one_telemetry(topic, e)
                else:
                    print(f"⬆️  {time.strftime('%H:%M:%S')} {topic} | {str(e)[:200]}")
            return
        # 单条对象
        _print_one_telemetry(topic, obj)
        return

    if isinstance(obj, list):
        for e in obj:
            if isinstance(e, dict):
                _print_one_telemetry(topic, e)
            else:
                print(f"⬆️  {time.strftime('%H:%M:%S')} {topic} | {str(e)[:200]}")
        return

    # 非 JSON：原样打印
    print(f"⬆️  {time.strftime('%H:%M:%S')} {topic} | {payload[:200]}")

# ========= MQTT 回调（v2签名） =========
def on_connect(client: mqtt.Client, userdata, flags, reason_code, properties=None):
    if reason_code == 0:
        print("✅ Connected to broker")
        for t in UPLINK_TOPICS:
            try:
                client.subscribe(t, qos=QOS_UP)
                print(f"🔔 Subscribed uplink: {t}")
            except Exception as e:
                print("❌ Subscribe failed:", e)
    else:
        print(f"❌ Connect failed rc={reason_code}")

def on_disconnect(client: mqtt.Client, userdata, reason_code, properties=None):
    print(f"⚠️ Disconnected. reason_code={reason_code}")

def on_message(client: mqtt.Client, userdata, msg: mqtt.MQTTMessage):
    try:
        payload = msg.payload.decode("utf-8", errors="replace")
        _handle_uplink_payload(msg.topic, payload)
    except Exception as e:
        print("⚠️ uplink print error:", e)

# ========= 主流程 =========
def main():
    # 1) 读取文件夹 -> {agv: waypoints[]}
    if not os.path.isdir(FOLDER):
        print(f"❌ 路径不存在或不是文件夹：{FOLDER}")
        sys.exit(1)

    agv_paths: Dict[str, List[dict]] = {}
    file_count = 0
    for name in os.listdir(FOLDER):
        full = os.path.join(FOLDER, name)
        if not os.path.isfile(full): continue
        _, ext = os.path.splitext(name)
        if ext.lower() not in EXTS: continue

        file_count += 1
        raw = tolerant_load(full)
        if raw is None:
            print(f"⛔ 跳过（无法解析）：{name}")
            continue

        fallback_agv = extract_agv_from_filename(name)
        agv, arr, mode = normalize_to_single(raw, fallback_agv)
        if agv is None or arr is None:
            print(f"⛔ 跳过（{mode}）：{name}")
            continue

        agv = agv.strip()
        agv_paths.setdefault(agv, []).extend(arr)
        print(f"📄 载入 {name} -> {agv} ({len(arr)} points, mode={mode})")

    if not agv_paths:
        print("❌ 没有读到任何有效的 AGV 路径数据。")
        sys.exit(1)

    print(f"✅ 总计 {len(agv_paths)} 辆车，来自 {file_count} 个文件。")

    agv_ids = sorted(agv_paths.keys(), key=lambda s: (len(s), s))
    cursors = {agv: 0 for agv in agv_ids}

    # 2) MQTT
    client = mqtt.Client(
        client_id=CLIENT_ID,
        protocol=mqtt.MQTTv311,
        callback_api_version=mqtt.CallbackAPIVersion.VERSION2
    )
    if USERNAME:
        client.username_pw_set(USERNAME, PASSWORD or None)

    client.on_connect = on_connect
    client.on_disconnect = on_disconnect
    client.on_message = on_message
    client.reconnect_delay_set(min_delay=1, max_delay=10)

    client.connect(BROKER, PORT, keepalive=60)
    client.loop_start()

    # 3) 循环：下行分批 + 持续监听上行
    try:
        first_batch = True
        while True:
            any_left = False

            for agv in agv_ids:
                arr = agv_paths[agv]
                cur = cursors[agv]
                if cur >= len(arr):
                    continue

                any_left = True
                end = min(cur + BATCH, len(arr))
                chunk = arr[cur:end]

                if first_batch:
                    ok, msg = validate_first_chunk(agv, chunk)
                    if not ok:
                        print(f"⚠️ {msg}，自动扩展。")
                        expand_end = end
                        base_loc = str(chunk[0].get("loc", "")).strip() if chunk else ""
                        while expand_end < len(arr):
                            nxt = arr[expand_end]
                            if str(nxt.get("loc", "")).strip() != base_loc:
                                expand_end += 1
                                break
                            expand_end += 1
                        expand_end = max(expand_end, cur + MIN_POINTS_FIRST_CHUNK)
                        expand_end = min(expand_end, len(arr))
                        chunk = arr[cur:expand_end]
                        end = expand_end

                topic = TOPIC_SINGLE.format(agv=agv)
                payload = json.dumps(chunk, ensure_ascii=False, separators=(",", ":"))

                if not client.is_connected():
                    print("⏳ MQTT 未连接，等待重连后继续发布…")
                    break

                r = client.publish(topic, payload, qos=QOS_DOWN, retain=False)
                if r.rc == mqtt.MQTT_ERR_SUCCESS:
                    left = max(0, len(arr) - end)
                    print(f"🚚 Downlink {agv} -> {topic}: +{len(chunk)} (remaining {left})")
                else:
                    print(f"❌ publish failed rc={r.rc} for {agv}")

                cursors[agv] = end

            first_batch = False
            time.sleep(INTERVAL if any_left else 0.5)  # 发完也继续保活，接收上行

    except KeyboardInterrupt:
        print("🛑 Stop by user")
    finally:
        try: client.loop_stop()
        except: pass
        try: client.disconnect()
        except: pass

if __name__ == "__main__":
    main()