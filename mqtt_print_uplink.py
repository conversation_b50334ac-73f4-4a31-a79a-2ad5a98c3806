# mqtt_print_uplink.py
# 订阅 Unity 端回传的 AGV 状态，实时打印

import paho.mqtt.client as mqtt, json

BROKER = "localhost"
PORT = 1883

def on_connect(c,u,f,rc,p=None):
    print("✅ uplink connected" if rc==0 else f"❌ rc={rc}")
    c.subscribe("agv/+/telemetry", qos=0)  # 单车上行
    c.subscribe("agv/telemetry", qos=0)    # 批量上行

def on_message(c,u,msg):
    try:
        data = json.loads(msg.payload.decode("utf-8"))
    except Exception:
        data = msg.payload[:200]
    print(f"⬆️  {msg.topic}: {data}")

def on_disconnect(c,u,*rest):
    print("⚠️ uplink disconnected", rest)
    try: c.reconnect()
    except Exception as e: print("reconnect failed:", e)

client = mqtt.Client(callback_api_version=mqtt.CallbackAPIVersion.VERSION2)
client.on_connect=on_connect
client.on_message=on_message
client.on_disconnect=on_disconnect

client.connect(BROKER, PORT, 60)
client.loop_forever()
